#!/usr/bin/env python
# -*- coding: utf-8 -*-
import glob
import os
import cv2 as cv
import numpy as np

def calculate_image_sharpness(image):
    """计算图像清晰度（拉普拉斯方差）"""
    gray = cv.cvtColor(image, cv.COLOR_BGR2GRAY)
    return cv.Laplacian(gray, cv.CV_64F).var()

def quick_quality_analysis(image_dir, grid_size=(11, 8)):
    """快速质量分析"""
    image_files = sorted(glob.glob(os.path.join(image_dir, "*.jpg")))
    
    print(f"Analyzing {len(image_files)} images...")
    
    results = []
    
    for i, image_path in enumerate(image_files):
        image = cv.imread(image_path)
        if image is None:
            continue
            
        # 检测角点
        found, corners = cv.findChessboardCorners(image, grid_size)
        
        if found:
            # 计算清晰度
            sharpness = calculate_image_sharpness(image)
            
            # 计算亮度统计
            gray = cv.cvtColor(image, cv.COLOR_BGR2GRAY)
            mean_brightness = np.mean(gray)
            std_brightness = np.std(gray)
            
            # 简单质量评分
            sharpness_score = min(sharpness / 10, 100)
            brightness_score = 100 - abs(mean_brightness - 130) * 0.5
            contrast_score = min(std_brightness * 2, 100)
            
            quality_score = (sharpness_score * 0.5 + brightness_score * 0.25 + contrast_score * 0.25)
            
            results.append({
                'filename': os.path.basename(image_path),
                'quality_score': quality_score,
                'sharpness': sharpness,
                'brightness': mean_brightness,
                'contrast': std_brightness
            })
    
    # 排序并显示结果
    results.sort(key=lambda x: x['quality_score'], reverse=True)
    
    print(f"\nFound {len(results)} images with detected corners")
    
    if results:
        scores = [r['quality_score'] for r in results]
        print(f"\nQuality Statistics:")
        print(f"Highest: {max(scores):.1f}")
        print(f"Lowest: {min(scores):.1f}")
        print(f"Average: {np.mean(scores):.1f}")
        print(f"Median: {np.median(scores):.1f}")
        
        print(f"\nTop 15 images:")
        for i, result in enumerate(results[:15]):
            print(f"{i+1:2d}. {result['filename']}: {result['quality_score']:.1f} "
                  f"(sharp:{result['sharpness']:.0f}, bright:{result['brightness']:.0f}, contrast:{result['contrast']:.0f})")
        
        # 建议阈值
        median_score = np.median(scores)
        suggested_threshold = max(30, median_score * 0.8)
        print(f"\nSuggested quality threshold: {suggested_threshold:.1f}")
        
        above_threshold = len([s for s in scores if s >= suggested_threshold])
        print(f"Images above suggested threshold: {above_threshold}")

if __name__ == '__main__':
    quick_quality_analysis("fisheye_calibration_images")
