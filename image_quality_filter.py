#!/usr/bin/env python
# -*- coding: utf-8 -*-
import argparse
import glob
import os
import shutil
import cv2 as cv
import numpy as np


def get_args():
    parser = argparse.ArgumentParser()
    
    parser.add_argument("--image_dir", type=str, required=True,
                       help="Directory containing calibration images")
    parser.add_argument("--output_dir", type=str, default="filtered_images",
                       help="Directory to save filtered high-quality images")
    parser.add_argument("--grid_size", type=str, default="11,8",
                       help="Chessboard grid size")
    parser.add_argument("--min_quality_score", type=float, default=70.0,
                       help="Minimum quality score to keep image")
    parser.add_argument("--max_images", type=int, default=50,
                       help="Maximum number of images to keep")
    
    return parser.parse_args()


def calculate_image_sharpness(image):
    """计算图像清晰度（拉普拉斯方差）"""
    gray = cv.cvtColor(image, cv.COLOR_BGR2GRAY)
    return cv.Laplacian(gray, cv.CV_64F).var()


def calculate_brightness_uniformity(image):
    """计算亮度均匀性"""
    gray = cv.cvtColor(image, cv.COLOR_BGR2GRAY)
    mean_brightness = np.mean(gray)
    std_brightness = np.std(gray)
    
    # 理想亮度范围：80-180，标准差>30表示对比度好
    brightness_score = 100 - abs(mean_brightness - 130) * 0.5  # 130是理想亮度
    contrast_score = min(std_brightness * 2, 100)  # 标准差越大对比度越好
    
    return (brightness_score + contrast_score) / 2


def calculate_corner_quality(corners, image_shape):
    """计算角点质量"""
    if corners is None:
        return 0
    
    h, w = image_shape[:2]
    
    # 1. 检查角点分布
    x_coords = corners[:, 0, 0]
    y_coords = corners[:, 0, 1]
    
    # 计算棋盘格覆盖面积
    min_x, max_x = np.min(x_coords), np.max(x_coords)
    min_y, max_y = np.min(y_coords), np.max(y_coords)
    
    coverage_area = (max_x - min_x) * (max_y - min_y)
    image_area = w * h
    coverage_ratio = coverage_area / image_area
    
    # 理想覆盖率：30%-70%
    if 0.3 <= coverage_ratio <= 0.7:
        coverage_score = 100
    elif 0.2 <= coverage_ratio <= 0.8:
        coverage_score = 80
    elif 0.1 <= coverage_ratio <= 0.9:
        coverage_score = 60
    else:
        coverage_score = 30
    
    # 2. 检查边缘距离
    edge_margin = min(w, h) * 0.05
    near_edge = (np.any(x_coords < edge_margin) or 
                np.any(x_coords > w - edge_margin) or
                np.any(y_coords < edge_margin) or 
                np.any(y_coords > h - edge_margin))
    
    edge_score = 80 if not near_edge else 50
    
    # 3. 检查角点分布均匀性
    x_std = np.std(x_coords)
    y_std = np.std(y_coords)
    distribution_score = min((x_std + y_std) / 10, 100)
    
    return (coverage_score + edge_score + distribution_score) / 3


def calculate_reprojection_error(corners, object_points, camera_matrix, dist_coeffs):
    """计算单张图像的重投影误差"""
    if corners is None:
        return float('inf')
    
    try:
        # 求解PnP问题
        success, rvec, tvec = cv.solvePnP(object_points, corners, camera_matrix, dist_coeffs)
        if not success:
            return float('inf')
        
        # 重投影
        projected_points, _ = cv.projectPoints(object_points, rvec, tvec, camera_matrix, dist_coeffs)
        
        # 计算误差
        error = cv.norm(corners, projected_points, cv.NORM_L2) / len(projected_points)
        return error
    except:
        return float('inf')


def analyze_image_quality(image_path, grid_size, camera_matrix=None, dist_coeffs=None, object_points=None):
    """分析单张图像的质量"""
    image = cv.imread(image_path)
    if image is None:
        return None
    
    # 检测角点
    found, corners = cv.findChessboardCorners(image, grid_size)
    
    if not found:
        return {
            'path': image_path,
            'corners_detected': False,
            'quality_score': 0,
            'sharpness': 0,
            'brightness': 0,
            'corner_quality': 0,
            'reprojection_error': float('inf')
        }
    
    # 亚像素精度优化
    gray = cv.cvtColor(image, cv.COLOR_BGR2GRAY)
    criteria = (cv.TERM_CRITERIA_EPS + cv.TERM_CRITERIA_MAX_ITER, 30, 0.1)
    cv.cornerSubPix(gray, corners, (3, 3), (-1, -1), criteria)
    
    # 计算各项质量指标
    sharpness = calculate_image_sharpness(image)
    brightness = calculate_brightness_uniformity(image)
    corner_quality = calculate_corner_quality(corners, image.shape)
    
    # 计算重投影误差（如果有相机参数）
    reprojection_error = float('inf')
    if camera_matrix is not None and dist_coeffs is not None and object_points is not None:
        reprojection_error = calculate_reprojection_error(corners, object_points, camera_matrix, dist_coeffs)
    
    # 综合质量评分
    sharpness_score = min(sharpness / 10, 100)  # 清晰度评分
    quality_score = (sharpness_score * 0.4 + brightness * 0.3 + corner_quality * 0.3)
    
    return {
        'path': image_path,
        'corners_detected': True,
        'corners': corners,
        'quality_score': quality_score,
        'sharpness': sharpness,
        'brightness': brightness,
        'corner_quality': corner_quality,
        'reprojection_error': reprojection_error
    }


def main():
    args = get_args()
    
    grid_size = tuple(map(int, args.grid_size.split(',')))
    
    # 创建3D物体点
    square_size = 60.0  # mm
    object_points = np.zeros((np.prod(grid_size), 3), np.float32)
    object_points[:, :2] = np.indices(grid_size).T.reshape(-1, 2) * square_size
    
    # 获取所有图像
    image_files = sorted(glob.glob(os.path.join(args.image_dir, "*.jpg")))
    
    if not image_files:
        print(f"No images found in {args.image_dir}")
        return
    
    print(f"Analyzing {len(image_files)} images...")
    
    # 分析所有图像
    image_analyses = []
    for i, image_path in enumerate(image_files):
        print(f"Analyzing {i+1}/{len(image_files)}: {os.path.basename(image_path)}")
        analysis = analyze_image_quality(image_path, grid_size, object_points=object_points)
        if analysis:
            image_analyses.append(analysis)
    
    # 过滤出检测到角点的图像
    valid_images = [img for img in image_analyses if img['corners_detected']]
    
    print(f"\nFound {len(valid_images)} images with detected corners")
    
    if not valid_images:
        print("No valid images found!")
        return
    
    # 按质量评分排序
    valid_images.sort(key=lambda x: x['quality_score'], reverse=True)
    
    # 应用质量阈值和数量限制
    filtered_images = [
        img for img in valid_images 
        if img['quality_score'] >= args.min_quality_score
    ][:args.max_images]
    
    print(f"\nFiltered to {len(filtered_images)} high-quality images")
    
    # 创建输出目录
    if not os.path.exists(args.output_dir):
        os.makedirs(args.output_dir)
    
    # 复制高质量图像
    for i, img_info in enumerate(filtered_images):
        src_path = img_info['path']
        filename = f"hq_{i:03d}_{os.path.basename(src_path)}"
        dst_path = os.path.join(args.output_dir, filename)
        shutil.copy2(src_path, dst_path)
        
        print(f"Copied: {filename} (score: {img_info['quality_score']:.1f})")
    
    # 生成质量报告
    report_path = os.path.join(args.output_dir, "quality_report.txt")
    with open(report_path, 'w') as f:
        f.write("Image Quality Analysis Report\n")
        f.write("=" * 50 + "\n\n")
        f.write(f"Total images analyzed: {len(image_analyses)}\n")
        f.write(f"Images with corners detected: {len(valid_images)}\n")
        f.write(f"High-quality images selected: {len(filtered_images)}\n")
        f.write(f"Quality threshold: {args.min_quality_score}\n")
        f.write(f"Max images: {args.max_images}\n\n")
        
        f.write("Selected Images:\n")
        f.write("-" * 30 + "\n")
        for i, img_info in enumerate(filtered_images):
            f.write(f"{i+1:2d}. {os.path.basename(img_info['path'])}\n")
            f.write(f"    Quality Score: {img_info['quality_score']:.1f}\n")
            f.write(f"    Sharpness: {img_info['sharpness']:.1f}\n")
            f.write(f"    Brightness: {img_info['brightness']:.1f}\n")
            f.write(f"    Corner Quality: {img_info['corner_quality']:.1f}\n\n")
    
    print(f"\nQuality report saved to: {report_path}")
    print(f"High-quality images saved to: {args.output_dir}")
    print("\nNow run calibration on the filtered images:")
    print(f"python 02-01_fisheyeCalibrateCamera_batch.py --image_dir {args.output_dir} --grid_size \"{args.grid_size}\" --no_display")


if __name__ == '__main__':
    main()
