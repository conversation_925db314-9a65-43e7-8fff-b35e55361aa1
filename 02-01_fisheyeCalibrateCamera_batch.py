#!/usr/bin/env python
# -*- coding: utf-8 -*-
import argparse
import glob
import os

import cv2 as cv
import numpy as np


def get_args():
    parser = argparse.ArgumentParser()

    parser.add_argument("--image_dir", type=str, 
                       default="fisheye_calibration/fisheye_calibration_filtered/23",
                       help="Directory containing calibration images")
    parser.add_argument("--image_pattern", type=str, default="*.jpg",
                       help="Image file pattern (e.g., *.jpg, *.png)")
    
    parser.add_argument("--square_len", type=float, default=60.0)  # 6cm = 60mm
    parser.add_argument("--grid_size", type=str, default="8,11")  # 8x11 内角点

    parser.add_argument("--k_filename", type=str, default="K_fisheye_batch.csv")
    parser.add_argument("--d_filename", type=str, default="d_fisheye_batch.csv")

    args = parser.parse_args()

    return args


def main():
    # コマンドライン引数
    args = get_args()

    image_dir = args.image_dir
    image_pattern = args.image_pattern
    square_side_length = args.square_len  # チェスボード内の正方形の1辺のサイズ(mm)
    grid_intersection_size = tuple(map(int, args.grid_size.split(',')))  # チェスボード内の格子数

    k_filename = args.k_filename
    d_filename = args.d_filename

    # 画像ファイルのリストを取得
    image_files = glob.glob(os.path.join(image_dir, image_pattern))
    image_files.sort()  # ファイル名でソート
    
    if len(image_files) == 0:
        print(f"No images found in {image_dir} with pattern {image_pattern}")
        return

    print(f"Found {len(image_files)} images for calibration")

    # チェスボードコーナー検出情報 保持用変数
    pattern_points = np.zeros((1, np.prod(grid_intersection_size), 3),
                              np.float32)
    pattern_points[0, :, :2] = np.indices(grid_intersection_size).T.reshape(
        -1, 2)
    pattern_points *= square_side_length
    object_points = []
    image_points = []

    subpix_criteria = (cv.TERM_CRITERIA_EPS + cv.TERM_CRITERIA_MAX_ITER, 30,
                       0.1)

    successful_detections = 0
    
    # 各画像でチェスボードコーナーを検出
    for i, image_file in enumerate(image_files):
        print(f"Processing {i+1}/{len(image_files)}: {os.path.basename(image_file)}")
        
        # 画像を読み込み
        frame = cv.imread(image_file)
        if frame is None:
            print(f"  Failed to load image: {image_file}")
            continue
            
        # チェスボードのコーナーを検出
        found, corner = cv.findChessboardCorners(frame, grid_intersection_size)

        if found:
            print(f"  ✓ Chessboard corners detected")
            
            # サブピクセル精度でコーナー位置を改善
            gray_image = cv.cvtColor(frame, cv.COLOR_BGR2GRAY)
            cv.cornerSubPix(gray_image, corner, (3, 3), (-1, -1),
                            subpix_criteria)
            
            # 検出結果を保存
            image_points.append(corner)
            object_points.append(pattern_points)
            successful_detections += 1
            
            # 結果を可視化（オプション）
            cv.drawChessboardCorners(frame, grid_intersection_size, corner, found)
            
            # 画像を表示（ESCで次の画像へ）
            cv.imshow('Detected Corners', frame)
            key = cv.waitKey(500) & 0xFF
            if key == 27:  # ESC
                break
        else:
            print(f"  ✗ Chessboard corners NOT detected")

    cv.destroyAllWindows()
    
    print(f"\nSuccessfully detected corners in {successful_detections}/{len(image_files)} images")

    if successful_detections > 0:
        # カメラ内部パラメータを計算
        print('\nStarting fisheye calibration...')

        calibration_flags = cv.fisheye.CALIB_RECOMPUTE_EXTRINSIC + cv.fisheye.CALIB_FIX_SKEW

        K = np.zeros((3, 3))
        d = np.zeros((4, 1))
        rvecs = [
            np.zeros((1, 1, 3), dtype=np.float64)
            for i in range(len(image_points))
        ]
        tvecs = [
            np.zeros((1, 1, 3), dtype=np.float64)
            for i in range(len(image_points))
        ]
        
        # 最初の画像から画像サイズを取得
        first_image = cv.imread(image_files[0])
        image_size = (first_image.shape[1], first_image.shape[0])
        
        rms, K, d, r, t = \
            cv.fisheye.calibrate(
                object_points,
                image_points,
                image_size,
                K,
                d,
                rvecs,
                tvecs,
                calibration_flags,
                (cv.TERM_CRITERIA_EPS + cv.TERM_CRITERIA_MAX_ITER, 30, 1e-6)
            )
            
        print(f"\nCalibration completed!")
        print(f"RMS = {rms}")
        print(f"Image size = {image_size}")
        print(f"K (Camera Matrix) = \n{K}")
        print(f"d (Distortion Coefficients) = {d.ravel()}")
        
        # 結果を保存
        np.savetxt(k_filename, K, delimiter=',', fmt="%0.14f")
        np.savetxt(d_filename, d, delimiter=',', fmt="%0.14f")
        
        print(f"\nCalibration parameters saved:")
        print(f"  Camera matrix: {k_filename}")
        print(f"  Distortion coefficients: {d_filename}")
        
    else:
        print("No successful corner detections. Cannot perform calibration.")


if __name__ == '__main__':
    main()
