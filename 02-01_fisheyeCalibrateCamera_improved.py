#!/usr/bin/env python
# -*- coding: utf-8 -*-
import argparse
import os
import datetime
import cv2 as cv
import numpy as np


def get_args():
    parser = argparse.ArgumentParser()

    parser.add_argument("--device", type=int, default=0)
    parser.add_argument("--file", type=str, default=None)
    parser.add_argument("--width", type=int, default=1280)
    parser.add_argument("--height", type=int, default=720)

    parser.add_argument("--square_len", type=float, default=60.0)  # 6cm = 60mm
    parser.add_argument("--grid_size", type=str, default="11,8")  # 11x8 内角点 (横向)

    parser.add_argument("--k_filename", type=str, default="K_fisheye.csv")
    parser.add_argument("--d_filename", type=str, default="d_fisheye.csv")
    
    parser.add_argument("--save_images", action='store_true', 
                       help="Save captured images for calibration")
    parser.add_argument("--image_dir", type=str, default="fisheye_calibration_improved",
                       help="Directory to save captured images")

    parser.add_argument("--interval_time", type=int, default=500)
    parser.add_argument('--use_autoappend', action='store_true')
    
    # 新增：质量控制参数
    parser.add_argument("--min_area_ratio", type=float, default=0.3,
                       help="Minimum chessboard area ratio in image")
    parser.add_argument("--max_area_ratio", type=float, default=0.8,
                       help="Maximum chessboard area ratio in image")

    args = parser.parse_args()
    return args


def calculate_chessboard_area_ratio(corners, image_shape):
    """计算棋盘格在图像中的面积比例"""
    if corners is None or len(corners) == 0:
        return 0.0
    
    # 计算棋盘格边界框
    x_coords = corners[:, 0, 0]
    y_coords = corners[:, 0, 1]
    
    min_x, max_x = np.min(x_coords), np.max(x_coords)
    min_y, max_y = np.min(y_coords), np.max(y_coords)
    
    chessboard_area = (max_x - min_x) * (max_y - min_y)
    image_area = image_shape[1] * image_shape[0]  # width * height
    
    return chessboard_area / image_area


def check_image_quality(frame, corners, grid_size):
    """检查图像质量"""
    quality_score = 0
    issues = []
    
    # 1. 检查图像清晰度（拉普拉斯方差）
    gray = cv.cvtColor(frame, cv.COLOR_BGR2GRAY)
    laplacian_var = cv.Laplacian(gray, cv.CV_64F).var()
    
    if laplacian_var > 500:
        quality_score += 30
    elif laplacian_var > 200:
        quality_score += 20
        issues.append("图像稍微模糊")
    else:
        quality_score += 10
        issues.append("图像模糊")
    
    # 2. 检查光照均匀性
    mean_brightness = np.mean(gray)
    std_brightness = np.std(gray)
    
    if 80 <= mean_brightness <= 180 and std_brightness > 30:
        quality_score += 25
    elif 60 <= mean_brightness <= 200:
        quality_score += 15
        issues.append("光照不够均匀")
    else:
        quality_score += 5
        issues.append("光照条件差")
    
    # 3. 检查角点检测质量
    if corners is not None:
        # 计算角点分布的均匀性
        area_ratio = calculate_chessboard_area_ratio(corners, frame.shape)
        
        if 0.3 <= area_ratio <= 0.7:
            quality_score += 25
        elif 0.2 <= area_ratio <= 0.8:
            quality_score += 15
            issues.append("棋盘格大小不够理想")
        else:
            quality_score += 5
            issues.append("棋盘格太大或太小")
        
        # 检查角点是否靠近边缘
        h, w = frame.shape[:2]
        edge_margin = min(w, h) * 0.05  # 5%边缘
        
        x_coords = corners[:, 0, 0]
        y_coords = corners[:, 0, 1]
        
        near_edge = np.any(x_coords < edge_margin) or np.any(x_coords > w - edge_margin) or \
                   np.any(y_coords < edge_margin) or np.any(y_coords > h - edge_margin)
        
        if not near_edge:
            quality_score += 20
        else:
            quality_score += 10
            issues.append("棋盘格太靠近边缘")
    
    return quality_score, issues


def main():
    # 命令行参数
    args = get_args()

    cap_device = args.device
    filepath = args.file
    cap_width = args.width
    cap_height = args.height

    # 摄像头准备
    cap = None
    if filepath is None:
        cap = cv.VideoCapture(cap_device)
        cap.set(cv.CAP_PROP_FRAME_WIDTH, cap_width)
        cap.set(cv.CAP_PROP_FRAME_HEIGHT, cap_height)
    else:
        cap = cv.VideoCapture(filepath)

    square_side_length = args.square_len  # 棋盘格内正方形的边长(mm)
    grid_intersection_size = eval(args.grid_size)  # 棋盘格内的格子数

    k_filename = args.k_filename
    d_filename = args.d_filename
    
    save_images = args.save_images
    image_dir = args.image_dir
    
    # 创建图片保存目录
    if save_images:
        if not os.path.exists(image_dir):
            os.makedirs(image_dir)
            print(f"创建图片保存目录: {image_dir}")

    interval_time = args.interval_time
    use_autoappend = args.use_autoappend
    if use_autoappend is False:
        interval_time = 10

    # 棋盘格角点检测信息 保存用变量
    pattern_points = np.zeros((1, np.prod(grid_intersection_size), 3), np.float32)
    pattern_points[0, :, :2] = np.indices(grid_intersection_size).T.reshape(-1, 2)
    pattern_points *= square_side_length
    object_points = []
    image_points = []

    subpix_criteria = (cv.TERM_CRITERIA_EPS + cv.TERM_CRITERIA_MAX_ITER, 30, 0.1)

    capture_count = 0
    high_quality_count = 0
    
    print("=== 改进的鱼眼相机标定图像采集 ===")
    print("目标：采集高质量图像以达到RMS < 0.2像素")
    print("建议：")
    print("1. 确保充足均匀的光照")
    print("2. 棋盘格要平整、清晰")
    print("3. 覆盖图像的不同区域和角度")
    print("4. 避免棋盘格太靠近边缘")
    print("=" * 50)
    
    while (True):
        ret, frame = cap.read()
        
        # 保存原始图像副本（用于保存）
        original_frame = frame.copy()

        # 检测棋盘格的角点
        found, corner = cv.findChessboardCorners(frame, grid_intersection_size)

        quality_score = 0
        issues = []
        
        if found:
            print('findChessboardCorners() : True')
            cv.drawChessboardCorners(frame, grid_intersection_size, corner, found)
            
            # 检查图像质量
            quality_score, issues = check_image_quality(original_frame, corner, grid_intersection_size)
            
        else:
            print('findChessboardCorners() : False')

        # 显示信息
        cv.putText(frame, f"Enter:Capture ({capture_count}, HQ:{high_quality_count})", 
                   (10, 30), cv.FONT_HERSHEY_SIMPLEX, 0.6, (0, 255, 0), 1)
        cv.putText(frame, "ESC: Complete", (10, 55),
                   cv.FONT_HERSHEY_SIMPLEX, 0.6, (0, 255, 0), 1)
        
        if found:
            # 显示质量评分
            color = (0, 255, 0) if quality_score >= 80 else (0, 255, 255) if quality_score >= 60 else (0, 0, 255)
            cv.putText(frame, f"Quality: {quality_score}/100", (10, 80),
                       cv.FONT_HERSHEY_SIMPLEX, 0.6, color, 1)
            
            # 显示问题
            for i, issue in enumerate(issues[:2]):  # 最多显示2个问题
                cv.putText(frame, issue, (10, 105 + i * 20),
                           cv.FONT_HERSHEY_SIMPLEX, 0.4, (0, 0, 255), 1)
        
        cv.imshow('Improved Fisheye Calibration', frame)

        key = cv.waitKey(interval_time) & 0xFF
        if ((use_autoappend is True) and found and quality_score >= 70) or \
           ((use_autoappend is False and key == 13) and found):  # Enter
            
            # 添加棋盘格角点检测信息
            gray_image = cv.cvtColor(original_frame, cv.COLOR_BGR2GRAY)
            cv.cornerSubPix(gray_image, corner, (3, 3), (-1, -1), subpix_criteria)
            image_points.append(corner)
            object_points.append(pattern_points)
            
            # 保存捕获的图片（使用原始图像，不包含角点标记）
            if save_images:
                timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
                quality_suffix = "HQ" if quality_score >= 80 else "MQ" if quality_score >= 60 else "LQ"
                image_filename = os.path.join(image_dir, 
                    f"fisheye_calib_{capture_count:03d}_{quality_suffix}_{quality_score:02d}_{timestamp}.jpg")
                cv.imwrite(image_filename, original_frame)
                print(f"保存图片: {image_filename} (质量: {quality_score}/100)")
                
                if quality_score >= 80:
                    high_quality_count += 1
            
            capture_count += 1
            
        if key == 27:  # ESC
            cap.release()
            cv.destroyAllWindows()
            break

    print(f"\n采集完成！")
    print(f"总图片数: {capture_count}")
    print(f"高质量图片数: {high_quality_count}")
    print(f"建议目标: 50-80张高质量图片")

    if len(image_points) > 0:
        # 计算摄像头内部参数
        print('fisheye.calibrate()')

        calibration_flags = cv.fisheye.CALIB_RECOMPUTE_EXTRINSIC + cv.fisheye.CALIB_FIX_SKEW

        K = np.zeros((3, 3))
        d = np.zeros((4, 1))
        rvecs = [np.zeros((1, 1, 3), dtype=np.float64) for i in range(len(image_points))]
        tvecs = [np.zeros((1, 1, 3), dtype=np.float64) for i in range(len(image_points))]
        
        rms, K, d, r, t = cv.fisheye.calibrate(
            object_points, image_points, gray_image.shape[::-1], K, d, rvecs, tvecs,
            calibration_flags, (cv.TERM_CRITERIA_EPS + cv.TERM_CRITERIA_MAX_ITER, 30, 1e-6)
        )
        
        print("RMS = " + str(rms))
        print("Shape=" + str((frame.shape[:2])[::-1]))
        print("K = \n", K)
        print("d = " + str(d.ravel()))
        np.savetxt(k_filename, K, delimiter=',', fmt="%0.14f")  # 保存摄像头内参矩阵
        np.savetxt(d_filename, d, delimiter=',', fmt="%0.14f")  # 保存畸变系数
    else:
        print("findChessboardCorners() not be successful once")

    cap.release()
    cv.destroyAllWindows()


if __name__ == '__main__':
    main()
