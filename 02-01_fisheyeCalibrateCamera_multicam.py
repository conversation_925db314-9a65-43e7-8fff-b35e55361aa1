#!/usr/bin/env python
# -*- coding: utf-8 -*-
import argparse
import glob
import os

import cv2 as cv
import numpy as np


def get_args():
    parser = argparse.ArgumentParser()

    parser.add_argument("--camera_id", type=int, default=0,
                       help="Camera ID (0, 1, 2, 3)")
    parser.add_argument("--image_dir", type=str, 
                       default="fisheye_calibration/fisheye_calibration_filtered",
                       help="Base directory containing calibration images")
    parser.add_argument("--image_pattern", type=str, default="*.jpg",
                       help="Image file pattern (e.g., *.jpg, *.png)")
    
    parser.add_argument("--square_len", type=float, default=60.0)  # 6cm = 60mm
    parser.add_argument("--grid_size", type=str, default="8,11")  # 8x11 内角点

    parser.add_argument("--output_dir", type=str, default="calibration_results",
                       help="Directory to save calibration results")

    args = parser.parse_args()

    return args


def find_camera_images(base_dir, camera_id, pattern="*.jpg"):
    """查找指定摄像头的标定图像"""
    
    # 搜索模式：包含 cam{camera_id} 的文件
    search_patterns = [
        os.path.join(base_dir, f"**/calib_cam{camera_id}_*{pattern[1:]}"),
        os.path.join(base_dir, f"calib_cam{camera_id}_*{pattern[1:]}"),
        os.path.join(base_dir, f"**/*cam{camera_id}*{pattern[1:]}"),
    ]
    
    image_files = []
    for search_pattern in search_patterns:
        files = glob.glob(search_pattern, recursive=True)
        image_files.extend(files)
    
    # 去重并排序
    image_files = list(set(image_files))
    image_files.sort()
    
    return image_files


def main():
    # コマンドライン引数
    args = get_args()

    camera_id = args.camera_id
    base_dir = args.image_dir
    image_pattern = args.image_pattern
    square_side_length = args.square_len  # チェスボード内の正方形の1辺のサイズ(mm)
    grid_intersection_size = tuple(map(int, args.grid_size.split(',')))  # チェスボード内の格子数
    output_dir = args.output_dir

    # 创建输出目录
    if not os.path.exists(output_dir):
        os.makedirs(output_dir)

    # 查找指定摄像头的图像
    image_files = find_camera_images(base_dir, camera_id, image_pattern)
    
    if len(image_files) == 0:
        print(f"No images found for camera {camera_id} in {base_dir}")
        print("Available files:")
        all_files = glob.glob(os.path.join(base_dir, "**/*.jpg"), recursive=True)
        for f in all_files[:10]:  # 显示前10个文件作为示例
            print(f"  {f}")
        return

    print(f"Found {len(image_files)} images for camera {camera_id}")
    print("Sample files:")
    for f in image_files[:5]:
        print(f"  {os.path.basename(f)}")

    # チェスボードコーナー検出情報 保持用変数
    pattern_points = np.zeros((1, np.prod(grid_intersection_size), 3),
                              np.float32)
    pattern_points[0, :, :2] = np.indices(grid_intersection_size).T.reshape(
        -1, 2)
    pattern_points *= square_side_length
    object_points = []
    image_points = []

    subpix_criteria = (cv.TERM_CRITERIA_EPS + cv.TERM_CRITERIA_MAX_ITER, 30,
                       0.1)

    successful_detections = 0
    
    # 各画像でチェスボードコーナーを検出
    for i, image_file in enumerate(image_files):
        print(f"Processing {i+1}/{len(image_files)}: {os.path.basename(image_file)}")
        
        # 画像を読み込み
        frame = cv.imread(image_file)
        if frame is None:
            print(f"  Failed to load image: {image_file}")
            continue
            
        # チェスボードのコーナーを検出
        found, corner = cv.findChessboardCorners(frame, grid_intersection_size)

        if found:
            print(f"  ✓ Chessboard corners detected")
            
            # サブピクセル精度でコーナー位置を改善
            gray_image = cv.cvtColor(frame, cv.COLOR_BGR2GRAY)
            cv.cornerSubPix(gray_image, corner, (3, 3), (-1, -1),
                            subpix_criteria)
            
            # 検出結果を保存
            image_points.append(corner)
            object_points.append(pattern_points)
            successful_detections += 1
            
            # 結果を可視化（オプション）
            cv.drawChessboardCorners(frame, grid_intersection_size, corner, found)
            
            # 画像を表示（ESCで次の画像へ）
            cv.imshow(f'Camera {camera_id} - Detected Corners', frame)
            key = cv.waitKey(300) & 0xFF
            if key == 27:  # ESC
                break
        else:
            print(f"  ✗ Chessboard corners NOT detected")

    cv.destroyAllWindows()
    
    print(f"\nCamera {camera_id}: Successfully detected corners in {successful_detections}/{len(image_files)} images")

    if successful_detections > 0:
        # カメラ内部パラメータを計算
        print(f'\nStarting fisheye calibration for camera {camera_id}...')

        calibration_flags = cv.fisheye.CALIB_RECOMPUTE_EXTRINSIC + cv.fisheye.CALIB_FIX_SKEW

        K = np.zeros((3, 3))
        d = np.zeros((4, 1))
        rvecs = [
            np.zeros((1, 1, 3), dtype=np.float64)
            for i in range(len(image_points))
        ]
        tvecs = [
            np.zeros((1, 1, 3), dtype=np.float64)
            for i in range(len(image_points))
        ]
        
        # 最初の画像から画像サイズを取得
        first_image = cv.imread(image_files[0])
        image_size = (first_image.shape[1], first_image.shape[0])
        
        rms, K, d, r, t = \
            cv.fisheye.calibrate(
                object_points,
                image_points,
                image_size,
                K,
                d,
                rvecs,
                tvecs,
                calibration_flags,
                (cv.TERM_CRITERIA_EPS + cv.TERM_CRITERIA_MAX_ITER, 30, 1e-6)
            )
            
        print(f"\nCalibration completed for camera {camera_id}!")
        print(f"RMS = {rms}")
        print(f"Image size = {image_size}")
        print(f"K (Camera Matrix) = \n{K}")
        print(f"d (Distortion Coefficients) = {d.ravel()}")
        
        # 結果を保存
        k_filename = os.path.join(output_dir, f"K_fisheye_cam{camera_id}.csv")
        d_filename = os.path.join(output_dir, f"d_fisheye_cam{camera_id}.csv")
        
        np.savetxt(k_filename, K, delimiter=',', fmt="%0.14f")
        np.savetxt(d_filename, d, delimiter=',', fmt="%0.14f")
        
        # 保存标定信息摘要
        summary_filename = os.path.join(output_dir, f"calibration_summary_cam{camera_id}.txt")
        with open(summary_filename, 'w') as f:
            f.write(f"Fisheye Camera {camera_id} Calibration Summary\n")
            f.write("=" * 50 + "\n")
            f.write(f"Date: {os.path.basename(image_files[0]).split('_')[3][:8]}\n")
            f.write(f"Total images processed: {len(image_files)}\n")
            f.write(f"Successful detections: {successful_detections}\n")
            f.write(f"Success rate: {successful_detections/len(image_files)*100:.1f}%\n")
            f.write(f"RMS error: {rms:.6f} pixels\n")
            f.write(f"Image size: {image_size}\n")
            f.write(f"Grid size: {grid_intersection_size}\n")
            f.write(f"Square size: {square_side_length} mm\n")
            f.write(f"\nCamera Matrix (K):\n{K}\n")
            f.write(f"\nDistortion Coefficients (d):\n{d.ravel()}\n")
        
        print(f"\nCalibration results saved:")
        print(f"  Camera matrix: {k_filename}")
        print(f"  Distortion coefficients: {d_filename}")
        print(f"  Summary: {summary_filename}")
        
    else:
        print(f"No successful corner detections for camera {camera_id}. Cannot perform calibration.")


if __name__ == '__main__':
    main()
