#!/usr/bin/env python
# -*- coding: utf-8 -*-
import cv2 as cv

def check_camera_resolution(device_id=0):
    """检查摄像头支持的分辨率"""
    print(f"检查摄像头 {device_id} 的分辨率...")
    
    cap = cv.VideoCapture(device_id)
    if not cap.isOpened():
        print(f"无法打开摄像头 {device_id}")
        return
    
    # 获取当前分辨率
    current_width = int(cap.get(cv.CAP_PROP_FRAME_WIDTH))
    current_height = int(cap.get(cv.CAP_PROP_FRAME_HEIGHT))
    print(f"当前分辨率: {current_width}x{current_height}")
    
    # 测试常见分辨率
    test_resolutions = [
        (1920, 1080),  # Full HD
        (1280, 720),   # HD
        (1024, 768),   # XGA
        (800, 600),    # SVGA
        (640, 480),    # VGA
        (320, 240),    # QVGA
    ]
    
    print("\n支持的分辨率:")
    supported_resolutions = []
    
    for width, height in test_resolutions:
        cap.set(cv.CAP_PROP_FRAME_WIDTH, width)
        cap.set(cv.CAP_PROP_FRAME_HEIGHT, height)
        
        actual_width = int(cap.get(cv.CAP_PROP_FRAME_WIDTH))
        actual_height = int(cap.get(cv.CAP_PROP_FRAME_HEIGHT))
        
        if actual_width == width and actual_height == height:
            print(f"  ✓ {width}x{height}")
            supported_resolutions.append((width, height))
        else:
            print(f"  ✗ {width}x{height} (实际: {actual_width}x{actual_height})")
    
    # 恢复到最高支持的分辨率
    if supported_resolutions:
        best_resolution = supported_resolutions[0]  # 第一个通常是最高分辨率
        cap.set(cv.CAP_PROP_FRAME_WIDTH, best_resolution[0])
        cap.set(cv.CAP_PROP_FRAME_HEIGHT, best_resolution[1])
        
        print(f"\n推荐使用分辨率: {best_resolution[0]}x{best_resolution[1]}")
        
        # 测试拍摄一帧
        ret, frame = cap.read()
        if ret:
            print(f"实际拍摄帧大小: {frame.shape[1]}x{frame.shape[0]}")
        else:
            print("无法读取摄像头帧")
    
    cap.release()
    return supported_resolutions

if __name__ == "__main__":
    check_camera_resolution()
