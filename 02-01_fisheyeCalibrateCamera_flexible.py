#!/usr/bin/env python
# -*- coding: utf-8 -*-
import argparse
import glob
import os
import cv2 as cv
import numpy as np


def get_args():
    parser = argparse.ArgumentParser()

    parser.add_argument("--image_dir", type=str, 
                       default="fisheye_calibration_images",
                       help="Directory containing calibration images")
    parser.add_argument("--image_pattern", type=str, default="*.jpg",
                       help="Image file pattern (e.g., *.jpg, *.png)")
    
    parser.add_argument("--square_len", type=float, default=60.0)  # 6cm = 60mm
    parser.add_argument("--grid_size", type=str, default="11,8")  # 11x8 内角点
    
    parser.add_argument("--k_filename", type=str, default="K_fisheye_flexible.csv")
    parser.add_argument("--d_filename", type=str, default="d_fisheye_flexible.csv")
    
    parser.add_argument("--no_display", action='store_true',
                       help="Disable image display (for headless environments)")
    
    # 灵活检测参数
    parser.add_argument("--use_adaptive_thresh", action='store_true',
                       help="Use adaptive threshold for corner detection")
    parser.add_argument("--normalize_image", action='store_true',
                       help="Normalize image before corner detection")

    args = parser.parse_args()
    return args


def enhance_image_for_detection(image, use_adaptive_thresh=False, normalize_image=False):
    """增强图像以提高角点检测成功率"""
    enhanced = image.copy()
    
    if normalize_image:
        # 直方图均衡化
        if len(enhanced.shape) == 3:
            enhanced = cv.cvtColor(enhanced, cv.COLOR_BGR2GRAY)
        enhanced = cv.equalizeHist(enhanced)
        enhanced = cv.cvtColor(enhanced, cv.COLOR_GRAY2BGR)
    
    if use_adaptive_thresh:
        # 自适应阈值处理
        gray = cv.cvtColor(enhanced, cv.COLOR_BGR2GRAY)
        adaptive = cv.adaptiveThreshold(gray, 255, cv.ADAPTIVE_THRESH_GAUSSIAN_C, 
                                       cv.THRESH_BINARY, 11, 2)
        enhanced = cv.cvtColor(adaptive, cv.COLOR_GRAY2BGR)
    
    return enhanced


def detect_corners_flexible(image, grid_size, use_adaptive_thresh=False, normalize_image=False):
    """使用多种方法尝试检测角点"""
    
    # 方法1：直接检测
    found, corners = cv.findChessboardCorners(image, grid_size)
    if found:
        return found, corners, "direct"
    
    # 方法2：增强图像后检测
    enhanced = enhance_image_for_detection(image, use_adaptive_thresh, normalize_image)
    found, corners = cv.findChessboardCorners(enhanced, grid_size)
    if found:
        return found, corners, "enhanced"
    
    # 方法3：使用不同的flags
    found, corners = cv.findChessboardCorners(image, grid_size, 
                                             cv.CALIB_CB_ADAPTIVE_THRESH + cv.CALIB_CB_NORMALIZE_IMAGE)
    if found:
        return found, corners, "adaptive_flags"
    
    # 方法4：缩放图像
    scale_factors = [0.8, 1.2, 0.6, 1.5]
    for scale in scale_factors:
        h, w = image.shape[:2]
        new_h, new_w = int(h * scale), int(w * scale)
        resized = cv.resize(image, (new_w, new_h))
        
        found, corners = cv.findChessboardCorners(resized, grid_size)
        if found:
            # 将角点坐标缩放回原始尺寸
            corners = corners / scale
            return found, corners, f"scaled_{scale}"
    
    return False, None, "failed"


def main():
    args = get_args()

    image_dir = args.image_dir
    image_pattern = args.image_pattern
    square_side_length = args.square_len
    grid_intersection_size = tuple(map(int, args.grid_size.split(',')))

    k_filename = args.k_filename
    d_filename = args.d_filename

    # 获取图像文件列表
    image_files = sorted(glob.glob(os.path.join(image_dir, image_pattern)))
    
    if not image_files:
        print(f"No images found in {image_dir} with pattern {image_pattern}")
        return

    print(f"Found {len(image_files)} images for calibration")

    # 棋盘格角点检测信息 保存用变量
    pattern_points = np.zeros((1, np.prod(grid_intersection_size), 3), np.float32)
    pattern_points[0, :, :2] = np.indices(grid_intersection_size).T.reshape(-1, 2)
    pattern_points *= square_side_length
    object_points = []
    image_points = []

    subpix_criteria = (cv.TERM_CRITERIA_EPS + cv.TERM_CRITERIA_MAX_ITER, 30, 0.1)

    successful_detections = 0
    detection_methods = {}
    
    # 各画像でチェスボードコーナーを検出
    for i, image_file in enumerate(image_files):
        print(f"Processing {i+1}/{len(image_files)}: {os.path.basename(image_file)}")
        
        # 画像を読み込み
        frame = cv.imread(image_file)
        if frame is None:
            print(f"  Failed to load image: {image_file}")
            continue
            
        # 灵活的角点检测
        found, corner, method = detect_corners_flexible(
            frame, grid_intersection_size, 
            args.use_adaptive_thresh, args.normalize_image
        )

        if found:
            print(f"  ✓ Chessboard corners detected (method: {method})")
            
            # 统计检测方法
            detection_methods[method] = detection_methods.get(method, 0) + 1
            
            # サブピクセル精度でコーナー位置を改善
            gray_image = cv.cvtColor(frame, cv.COLOR_BGR2GRAY)
            cv.cornerSubPix(gray_image, corner, (3, 3), (-1, -1), subpix_criteria)
            
            # 検出結果を保存
            image_points.append(corner)
            object_points.append(pattern_points)
            successful_detections += 1
            
            # 結果を可視化（オプション）
            if not args.no_display:
                cv.drawChessboardCorners(frame, grid_intersection_size, corner, found)
                
                # 画像を表示（ESCで次の画像へ）
                cv.imshow('Detected Corners', frame)
                key = cv.waitKey(500) & 0xFF
                if key == 27:  # ESC
                    break
        else:
            print(f"  ✗ Chessboard corners NOT detected")

    if not args.no_display:
        cv.destroyAllWindows()
    
    print(f"\nSuccessfully detected corners in {successful_detections}/{len(image_files)} images")
    print("Detection methods used:")
    for method, count in detection_methods.items():
        print(f"  {method}: {count} images")

    if successful_detections > 0:
        # カメラ内部パラメータを計算
        print('\nStarting fisheye calibration...')

        calibration_flags = cv.fisheye.CALIB_RECOMPUTE_EXTRINSIC + cv.fisheye.CALIB_FIX_SKEW

        K = np.zeros((3, 3))
        d = np.zeros((4, 1))
        rvecs = [np.zeros((1, 1, 3), dtype=np.float64) for i in range(len(image_points))]
        tvecs = [np.zeros((1, 1, 3), dtype=np.float64) for i in range(len(image_points))]
        
        # 最初の画像から画像サイズを取得
        first_image = cv.imread(image_files[0])
        image_size = (first_image.shape[1], first_image.shape[0])
        
        rms, K, d, r, t = cv.fisheye.calibrate(
            object_points, image_points, image_size, K, d, rvecs, tvecs,
            calibration_flags, (cv.TERM_CRITERIA_EPS + cv.TERM_CRITERIA_MAX_ITER, 30, 1e-6)
        )
            
        print(f"\nCalibration completed!")
        print(f"RMS = {rms}")
        print(f"Image size = {image_size}")
        print(f"K (Camera Matrix) = \n{K}")
        print(f"d (Distortion Coefficients) = {d.ravel()}")
        
        # 結果を保存
        np.savetxt(k_filename, K, delimiter=',', fmt="%0.14f")
        np.savetxt(d_filename, d, delimiter=',', fmt="%0.14f")
        
        print(f"\nCalibration parameters saved:")
        print(f"  Camera matrix: {k_filename}")
        print(f"  Distortion coefficients: {d_filename}")
        
    else:
        print("No successful corner detections. Cannot perform calibration.")


if __name__ == '__main__':
    main()
