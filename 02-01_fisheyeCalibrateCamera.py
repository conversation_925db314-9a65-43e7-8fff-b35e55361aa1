#!/usr/bin/env python
# -*- coding: utf-8 -*-
import argparse
import os
import datetime

import cv2 as cv
import numpy as np


def calculate_image_quality_score(image, corners=None):
    """计算图像质量评分"""
    if image is None:
        return 0, []

    issues = []
    quality_score = 0

    # 1. 计算清晰度（拉普拉斯方差）
    gray = cv.cvtColor(image, cv.COLOR_BGR2GRAY)
    laplacian_var = cv.Laplacian(gray, cv.CV_64F).var()

    if laplacian_var > 500:
        sharpness_score = 30
    elif laplacian_var > 200:
        sharpness_score = 20
        issues.append("图像稍微模糊")
    else:
        sharpness_score = 10
        issues.append("图像模糊")

    quality_score += sharpness_score

    # 2. 计算亮度和对比度
    mean_brightness = np.mean(gray)
    std_brightness = np.std(gray)

    # 亮度评分（理想范围80-180）
    brightness_score = max(0, 25 - abs(mean_brightness - 130) * 0.2)

    # 对比度评分（标准差>30较好）
    contrast_score = min(std_brightness * 0.5, 25)

    if mean_brightness < 60 or mean_brightness > 200:
        issues.append("亮度过低或过高")
    elif std_brightness < 20:
        issues.append("对比度不足")

    quality_score += brightness_score + contrast_score

    # 3. 角点质量评分
    if corners is not None:
        h, w = image.shape[:2]

        # 计算棋盘格覆盖面积
        x_coords = corners[:, 0, 0]
        y_coords = corners[:, 0, 1]

        min_x, max_x = np.min(x_coords), np.max(x_coords)
        min_y, max_y = np.min(y_coords), np.max(y_coords)

        coverage_area = (max_x - min_x) * (max_y - min_y)
        image_area = w * h
        coverage_ratio = coverage_area / image_area

        if 0.3 <= coverage_ratio <= 0.7:
            coverage_score = 20
        elif 0.2 <= coverage_ratio <= 0.8:
            coverage_score = 15
            issues.append("棋盘格大小不够理想")
        else:
            coverage_score = 5
            issues.append("棋盘格太大或太小")

        quality_score += coverage_score

    return min(quality_score, 100), issues


def get_args():
    parser = argparse.ArgumentParser()

    parser.add_argument("--device", type=int, default=0)
    parser.add_argument("--file", type=str, default=None)
    parser.add_argument("--width", type=int, default=1280)
    parser.add_argument("--height", type=int, default=720)

    parser.add_argument("--square_len", type=float, default=60.0)  # 6cm = 60mm
    parser.add_argument("--grid_size", type=str, default="11,8")  # 11x8 内角点 (横向)

    parser.add_argument("--k_filename", type=str, default="K_fisheye.csv")
    parser.add_argument("--d_filename", type=str, default="d_fisheye.csv")

    parser.add_argument("--save_images", action='store_true',
                       help="Save captured images for calibration")
    parser.add_argument("--image_dir", type=str, default="fisheye_calibration_images",
                       help="Directory to save captured images")

    parser.add_argument("--min_quality", type=float, default=55.0,
                       help="Minimum quality score for auto-save (default: 55)")

    parser.add_argument("--interval_time", type=int, default=500)
    parser.add_argument('--use_autoappend', action='store_true')

    args = parser.parse_args()

    return args


def main():
    # 命令行参数
    args = get_args()

    cap_device = args.device
    filepath = args.file
    cap_width = args.width
    cap_height = args.height

    # 摄像头准备
    cap = None
    if filepath is None:
        cap = cv.VideoCapture(cap_device)
        cap.set(cv.CAP_PROP_FRAME_WIDTH, cap_width)
        cap.set(cv.CAP_PROP_FRAME_HEIGHT, cap_height)
    else:
        cap = cv.VideoCapture(filepath)

    square_side_length = args.square_len  # 棋盘格内正方形的边长(mm)
    grid_intersection_size = eval(args.grid_size)  # 棋盘格内的格子数

    k_filename = args.k_filename
    d_filename = args.d_filename

    save_images = args.save_images
    image_dir = args.image_dir
    min_quality = args.min_quality

    # 创建图片保存目录
    if save_images:
        if not os.path.exists(image_dir):
            os.makedirs(image_dir)
            print(f"创建图片保存目录: {image_dir}")
        print(f"自动保存质量阈值: {min_quality}分")

    interval_time = args.interval_time
    use_autoappend = args.use_autoappend
    if use_autoappend is False:
        interval_time = 10

    # 棋盘格角点检测信息 保存用变量
    pattern_points = np.zeros((1, np.prod(grid_intersection_size), 3),
                              np.float32)
    pattern_points[0, :, :2] = np.indices(grid_intersection_size).T.reshape(
        -1, 2)
    pattern_points *= square_side_length
    object_points = []
    image_points = []

    subpix_criteria = (cv.TERM_CRITERIA_EPS + cv.TERM_CRITERIA_MAX_ITER, 30,
                       0.1)

    capture_count = 0
    while (True):
        ret, frame = cap.read()

        # 保存原始图像副本（用于保存）
        original_frame = frame.copy()

        # 检测棋盘格的角点
        found, corner = cv.findChessboardCorners(frame, grid_intersection_size)

        # 计算图像质量评分
        quality_score, issues = calculate_image_quality_score(original_frame, corner if found else None)

        if found:
            print(f'findChessboardCorners() : True (质量评分: {quality_score:.1f}/100)')
            cv.drawChessboardCorners(frame, grid_intersection_size, corner, found)
        else:
            print(f'findChessboardCorners() : False (质量评分: {quality_score:.1f}/100)')

        # 显示基本信息
        cv.putText(frame, f"Enter:Capture ({capture_count})",
                   (10, 30), cv.FONT_HERSHEY_SIMPLEX, 0.6, (0, 255, 0), 1)
        cv.putText(frame, "ESC: Complete", (10, 55),
                   cv.FONT_HERSHEY_SIMPLEX, 0.6, (0, 255, 0), 1)

        # 显示质量评分
        if found:
            # 根据质量评分选择颜色
            if quality_score >= 80:
                color = (0, 255, 0)  # 绿色 - 优秀
                quality_text = "优秀"
            elif quality_score >= 60:
                color = (0, 255, 255)  # 黄色 - 良好
                quality_text = "良好"
            else:
                color = (0, 0, 255)  # 红色 - 需要改进
                quality_text = "需要改进"

            cv.putText(frame, f"质量: {quality_score:.1f}/100 ({quality_text})",
                       (10, 80), cv.FONT_HERSHEY_SIMPLEX, 0.6, color, 2)

            # 显示问题提示（最多显示2个）
            for i, issue in enumerate(issues[:2]):
                cv.putText(frame, issue, (10, 105 + i * 20),
                           cv.FONT_HERSHEY_SIMPLEX, 0.4, (0, 0, 255), 1)
        else:
            cv.putText(frame, "未检测到棋盘格", (10, 80),
                       cv.FONT_HERSHEY_SIMPLEX, 0.6, (0, 0, 255), 1)

        cv.imshow('Fisheye Calibration with Quality Score', frame)

        key = cv.waitKey(interval_time) & 0xFF

        # 保存图片逻辑
        save_this_frame = False
        save_reason = ""

        if use_autoappend is True:
            # 自动模式：只保存检测到角点且质量达标的图像
            if found and quality_score >= min_quality:
                save_this_frame = True
                save_reason = f"自动保存(质量:{quality_score:.1f})"
            elif found:
                print(f"质量不达标，跳过保存 (质量:{quality_score:.1f} < {min_quality})")
        elif key == 13:  # Enter键手动保存
            if found:
                save_this_frame = True
                save_reason = f"手动保存(质量:{quality_score:.1f})"
            else:
                print("未检测到角点，无法保存")

        if save_this_frame and save_images:
            timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
            quality_level = "HQ" if quality_score >= 80 else "MQ" if quality_score >= 60 else "LQ"
            image_filename = os.path.join(image_dir,
                f"fisheye_calib_{capture_count:03d}_{quality_level}_{quality_score:02.0f}_{timestamp}.jpg")
            cv.imwrite(image_filename, original_frame)
            print(f"保存图片: {os.path.basename(image_filename)} ({save_reason})")
            capture_count += 1

        # 角点信息收集（仅用于实时标定，不影响图片保存）
        if found:
            gray_image = cv.cvtColor(frame, cv.COLOR_BGR2GRAY)
            cv.cornerSubPix(gray_image, corner, (3, 3), (-1, -1),
                            subpix_criteria)
            image_points.append(corner)
            object_points.append(pattern_points)
        if key == 27:  # ESC
            cap.release()
            cv.destroyAllWindows()
            break

    if len(image_points) > 0:
        # 计算摄像头内部参数
        print('fisheye.calibrate()')

        # calibration_flags = cv.fisheye.CALIB_RECOMPUTE_EXTRINSIC + cv.fisheye.CALIB_CHECK_COND + cv.fisheye.CALIB_FIX_SKEW
        calibration_flags = cv.fisheye.CALIB_RECOMPUTE_EXTRINSIC + cv.fisheye.CALIB_FIX_SKEW

        K = np.zeros((3, 3))
        d = np.zeros((4, 1))
        rvecs = [
            np.zeros((1, 1, 3), dtype=np.float64)
            for i in range(len(image_points))
        ]
        tvecs = [
            np.zeros((1, 1, 3), dtype=np.float64)
            for i in range(len(image_points))
        ]
        rms, K, d, r, t = \
            cv.fisheye.calibrate(
                object_points,
                image_points,
                gray_image.shape[::-1],
                K,
                d,
                rvecs,
                tvecs,
                calibration_flags,
                (cv.TERM_CRITERIA_EPS + cv.TERM_CRITERIA_MAX_ITER, 30, 1e-6)
            )
        print("RMS = " + str(rms))
        print("Shape=" + str((frame.shape[:2])[::-1]))
        print("K = \n", K)
        print("d = " + str(d.ravel()))
        np.savetxt(k_filename, K, delimiter=',', fmt="%0.14f")  # 保存摄像头内参矩阵
        np.savetxt(d_filename, d, delimiter=',', fmt="%0.14f")  # 保存畸变系数
    else:
        print("findChessboardCorners() not be successful once")

    cap.release()
    cv.destroyAllWindows()


if __name__ == '__main__':
    main()
