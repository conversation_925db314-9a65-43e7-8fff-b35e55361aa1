#!/usr/bin/env python
# -*- coding: utf-8 -*-
import argparse
import glob
import os

import cv2 as cv
import numpy as np


def get_args():
    parser = argparse.ArgumentParser()

    parser.add_argument("--device", type=int, default=0)
    parser.add_argument("--file", type=str, default=None)
    parser.add_argument("--width", type=int, default=640)
    parser.add_argument("--height", type=int, default=360)
    
    # 测试图像目录
    parser.add_argument("--test_image_dir", type=str, 
                       default="fisheye_calibration/fisheye_calibration_filtered/23",
                       help="Directory containing test images")
    parser.add_argument("--output_dir", type=str, default="undistorted_results",
                       help="Directory to save undistorted images")

    parser.add_argument("--k_new_param", type=float, default=0.9)

    parser.add_argument("--camera_id", type=int, default=0,
                       help="Camera ID (0, 1, 2, 3)")
    parser.add_argument("--calibration_dir", type=str, default="calibration_results",
                       help="Directory containing calibration files")
    parser.add_argument("--k_filename", type=str, default="",
                       help="Camera matrix file (auto-generated if empty)")
    parser.add_argument("--d_filename", type=str, default="",
                       help="Distortion coefficients file (auto-generated if empty)")
    
    parser.add_argument("--mode", type=str, choices=['camera', 'batch'], default='camera',
                       help="Mode: 'camera' for real-time, 'batch' for processing images")

    args = parser.parse_args()

    return args


def process_batch_images(args, camera_mat, dist_coef, new_camera_mat):
    """批处理模式：处理目录中的所有图像"""
    
    # 创建输出目录
    if not os.path.exists(args.output_dir):
        os.makedirs(args.output_dir)
    
    # 获取测试图像列表
    image_files = glob.glob(os.path.join(args.test_image_dir, "*.jpg"))
    image_files.extend(glob.glob(os.path.join(args.test_image_dir, "*.png")))
    image_files.sort()
    
    if len(image_files) == 0:
        print(f"No images found in {args.test_image_dir}")
        return
    
    print(f"Processing {len(image_files)} images...")
    
    for i, image_file in enumerate(image_files):
        print(f"Processing {i+1}/{len(image_files)}: {os.path.basename(image_file)}")
        
        # 读取图像
        frame = cv.imread(image_file)
        if frame is None:
            print(f"  Failed to load: {image_file}")
            continue
        
        # 鱼眼畸变校正
        undistort_image = cv.fisheye.undistortImage(
            frame,
            camera_mat,
            D=dist_coef,
            Knew=new_camera_mat,
        )
        
        # 保存校正后的图像
        base_name = os.path.splitext(os.path.basename(image_file))[0]
        output_path = os.path.join(args.output_dir, f"{base_name}_undistorted.jpg")
        cv.imwrite(output_path, undistort_image)
        
        # 显示对比结果
        # 调整图像大小以便显示
        display_height = 400
        h, w = frame.shape[:2]
        display_width = int(w * display_height / h)
        
        frame_resized = cv.resize(frame, (display_width, display_height))
        undistort_resized = cv.resize(undistort_image, (display_width, display_height))
        
        # 水平拼接显示
        comparison = np.hstack([frame_resized, undistort_resized])
        
        # 添加文字标签
        cv.putText(comparison, "Original", (10, 30), 
                  cv.FONT_HERSHEY_SIMPLEX, 1, (0, 255, 0), 2)
        cv.putText(comparison, "Undistorted", (display_width + 10, 30), 
                  cv.FONT_HERSHEY_SIMPLEX, 1, (0, 255, 0), 2)
        
        cv.imshow('Fisheye Undistortion Comparison', comparison)
        
        key = cv.waitKey(500) & 0xFF
        if key == 27:  # ESC
            break
        elif key == ord('s'):  # 's'键暂停
            cv.waitKey(0)
    
    cv.destroyAllWindows()
    print(f"\nBatch processing completed!")
    print(f"Undistorted images saved to: {args.output_dir}")


def process_camera_realtime(args, camera_mat, dist_coef, new_camera_mat):
    """实时摄像头模式"""
    
    # カメラ準備
    cap = None
    if args.file is None:
        cap = cv.VideoCapture(args.device)
        cap.set(cv.CAP_PROP_FRAME_WIDTH, args.width)
        cap.set(cv.CAP_PROP_FRAME_HEIGHT, args.height)
    else:
        cap = cv.VideoCapture(args.file)

    print("Real-time fisheye undistortion started. Press ESC to exit.")

    while True:
        ret, frame = cap.read()
        if not ret:
            break
            
        undistort_image = cv.fisheye.undistortImage(
            frame,
            camera_mat,
            D=dist_coef,
            Knew=new_camera_mat,
        )

        cv.imshow('Original', frame)
        cv.imshow('Undistorted', undistort_image)

        key = cv.waitKey(1) & 0xFF
        if key == 27:  # ESC
            break

    cap.release()
    cv.destroyAllWindows()


def main():
    # コマンドライン引数
    args = get_args()

    camera_id = args.camera_id
    calibration_dir = args.calibration_dir
    k_new_param = args.k_new_param

    # 自动生成文件名（如果未指定）
    if args.k_filename == "":
        k_filename = os.path.join(calibration_dir, f"K_fisheye_cam{camera_id}.csv")
    else:
        k_filename = args.k_filename

    if args.d_filename == "":
        d_filename = os.path.join(calibration_dir, f"d_fisheye_cam{camera_id}.csv")
    else:
        d_filename = args.d_filename

    # キャリブレーションデータの読み込み
    try:
        camera_mat = np.loadtxt(k_filename, delimiter=',')
        dist_coef = np.loadtxt(d_filename, delimiter=',')
        print(f"Loaded calibration data for camera {camera_id}:")
        print(f"  Camera matrix from: {k_filename}")
        print(f"  Distortion coefficients from: {d_filename}")
        print(f"  K_new scale parameter: {k_new_param}")
    except FileNotFoundError as e:
        print(f"Error: Calibration file not found: {e}")
        print(f"Please run the calibration for camera {camera_id} first!")
        print(f"Expected files:")
        print(f"  {k_filename}")
        print(f"  {d_filename}")
        return

    # 新しいカメラ行列を計算
    new_camera_mat = camera_mat.copy()
    new_camera_mat[(0, 1), (0, 1)] = k_new_param * new_camera_mat[(0, 1), (0, 1)]

    print(f"\nOriginal camera matrix:\n{camera_mat}")
    print(f"\nNew camera matrix (scaled by {k_new_param}):\n{new_camera_mat}")
    print(f"\nDistortion coefficients: {dist_coef}")

    # モード選択
    if args.mode == 'batch':
        process_batch_images(args, camera_mat, dist_coef, new_camera_mat)
    else:
        process_camera_realtime(args, camera_mat, dist_coef, new_camera_mat)


if __name__ == '__main__':
    main()
